@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer base {
  :root {
    /* TweakCN NEONPRO Healthcare Theme - Enhanced Foundation */
    /* Core Brand Colors - Brazilian Aesthetic Clinic Optimized */
    --primary: oklch(0.5854 0.2041 277.1173);
    --primary-foreground: oklch(1 0 0);
    --primary-dark: oklch(0.4568 0.2146 277.0229);
    --primary-light: oklch(0.6801 0.1583 276.9349);

    --secondary: oklch(0.8687 0.0043 56.366);
    --secondary-foreground: oklch(0.4461 0.0263 256.8018);

    --accent: oklch(0.9376 0.026 321.9388);
    --accent-foreground: oklch(0.3729 0.0306 259.7328);

    /* Core Interface Colors - WCAG 2.1 AA+ Compliant */
    --background: oklch(0.9232 0.0026 48.7171);
    --foreground: oklch(0.2795 0.0368 260.031);

    --card: oklch(0.9699 0.0013 106.4238);
    --card-foreground: oklch(0.2795 0.0368 260.031);

    --popover: oklch(0.9699 0.0013 106.4238);
    --popover-foreground: oklch(0.2795 0.0368 260.031);

    --muted: oklch(0.9232 0.0026 48.7171);
    --muted-foreground: oklch(0.551 0.0234 264.3637);

    /* Interactive Elements - Enhanced for Healthcare */
    --border: oklch(0.8687 0.0043 56.366);
    --input: oklch(0.8687 0.0043 56.366);
    --ring: oklch(0.5854 0.2041 277.1173);

    /* Status Colors - Healthcare Specific */
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1 0 0);

    --success: oklch(0.6792 0.1943 142.495);
    --success-foreground: oklch(1 0 0);

    --warning: oklch(0.7851 0.1274 83.872);
    --warning-foreground: oklch(0.2795 0.0368 260.031);

    --info: oklch(0.6228 0.1456 252.896);
    --info-foreground: oklch(1 0 0);

    /* Healthcare Status Colors - Critical for Patient Safety */
    --status-critical: oklch(0.6368 0.2078 25.3313);
    --status-urgent: oklch(0.7851 0.1274 83.872);
    --status-normal: oklch(0.6792 0.1943 142.495);
    --status-inactive: oklch(0.551 0.0234 264.3637);

    /* LGPD Compliance Colors - Brazil Legal Requirements */
    --lgpd-compliant: oklch(0.6792 0.1943 142.495);
    --lgpd-warning: oklch(0.7851 0.1274 83.872);
    --lgpd-violation: oklch(0.6368 0.2078 25.3313);

    /* Chart Colors - Enhanced Healthcare Analytics */
    --chart-1: oklch(0.5854 0.2041 277.1173);
    --chart-2: oklch(0.5106 0.2301 276.9656);
    --chart-3: oklch(0.4568 0.2146 277.0229);
    --chart-4: oklch(0.3984 0.1773 277.3662);
    --chart-5: oklch(0.3588 0.1354 278.6973);
    --chart-6: oklch(0.6792 0.1943 142.495);

    /* Sidebar Colors - Professional Healthcare Navigation */
    --sidebar-background: oklch(0.8687 0.0043 56.366);
    --sidebar-foreground: oklch(0.2795 0.0368 260.031);
    --sidebar-primary: oklch(0.5854 0.2041 277.1173);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.9376 0.026 321.9388);
    --sidebar-accent-foreground: oklch(0.3729 0.0306 259.7328);
    --sidebar-border: oklch(0.8687 0.0043 56.366);
    --sidebar-ring: oklch(0.5854 0.2041 277.1173);

    /* Typography & Layout - Medical Terminology Optimized */
    --radius: 1.25rem;
    --radius-sm: 0.75rem;
    --radius-md: 1rem;
    --radius-lg: 1.5rem;
    --radius-xl: 2rem;

    /* Font Families - Portuguese Medical Content */
    --font-sans: "Inter", "Segoe UI", system-ui, sans-serif;
    --font-serif: "Lora", "Georgia", serif;
    --font-mono: "JetBrains Mono", "Consolas", monospace;

    /* Healthcare Shadows - Professional Medical Interface */
    --shadow-color: oklch(0.4 0.02 260);
    --shadow-opacity: 0.18;
    --shadow-blur: 10px;
    --shadow-spread: 4px;
    --shadow-offset-x: 2px;
    --shadow-offset-y: 2px;

    --shadow-2xs: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.09);
    --shadow-xs: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.09);
    --shadow-sm: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.18), 2px 1px 2px 3px oklch(0.4 0.02 260 / 0.18);
    --shadow: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.18), 2px 1px 2px 3px oklch(0.4 0.02 260 / 0.18);
    --shadow-md: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.18), 2px 2px 4px 3px oklch(0.4 0.02 260 / 0.18);
    --shadow-lg: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.18), 2px 4px 6px 3px oklch(0.4 0.02 260 / 0.18);
    --shadow-xl: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.18), 2px 8px 10px 3px oklch(0.4 0.02 260 / 0.18);
    --shadow-2xl: 2px 2px 10px 4px oklch(0.4 0.02 260 / 0.45);

    /* Accessibility Enhancement Variables */
    --focus-ring-width: 3px;
    --focus-ring-color: oklch(0.5854 0.2041 277.1173);
    --focus-ring-offset: 2px;

    /* Motion & Animation - Reduced for Healthcare */
    --motion-scale: 1;
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 200ms;
    --animation-duration-slow: 300ms;
    --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    /* TweakCN NEONPRO Dark Mode - Healthcare Night Interface */
    --background: oklch(0.2392 0.0388 252.5089);
    --foreground: oklch(0.8569 0.0111 95.1836);

    --primary: oklch(0.6776 0.0653 81.7406);
    --primary-foreground: oklch(0.8569 0.0111 95.1836);
    --primary-dark: oklch(0.5854 0.0553 81.7406);
    --primary-light: oklch(0.7776 0.0753 81.7406);

    --secondary: oklch(0.2392 0.0388 252.5089);
    --secondary-foreground: oklch(0.8569 0.0111 95.1836);

    --accent: oklch(0.3896 0.0074 59.4734);
    --accent-foreground: oklch(0.8717 0.0093 258.3382);

    --card: oklch(0.2392 0.0388 252.5089);
    --card-foreground: oklch(0.8569 0.0111 95.1836);

    --popover: oklch(0.2392 0.0388 252.5089);
    --popover-foreground: oklch(0.8569 0.0111 95.1836);

    --muted: oklch(0.6776 0.0653 81.7406);
    --muted-foreground: oklch(0.8569 0.0111 95.1836);

    --border: oklch(0.3359 0.0077 59.4197);
    --input: oklch(0.3359 0.0077 59.4197);
    --ring: oklch(0.6776 0.0653 81.7406);

    /* Status Colors Dark Mode - Enhanced Visibility */
    --destructive: oklch(0.7368 0.2578 25.3313);
    --destructive-foreground: oklch(0.2244 0.0074 67.437);

    --success: oklch(0.7792 0.2243 142.495);
    --success-foreground: oklch(0.2392 0.0388 252.5089);

    --warning: oklch(0.8851 0.1574 83.872);
    --warning-foreground: oklch(0.2392 0.0388 252.5089);

    --info: oklch(0.7228 0.1856 252.896);
    --info-foreground: oklch(0.2392 0.0388 252.5089);

    /* Healthcare Status Colors Dark Mode */
    --status-critical: oklch(0.7368 0.2578 25.3313);
    --status-urgent: oklch(0.8851 0.1574 83.872);
    --status-normal: oklch(0.7792 0.2243 142.495);
    --status-inactive: oklch(0.651 0.0334 264.3637);

    /* LGPD Colors Dark Mode */
    --lgpd-compliant: oklch(0.7792 0.2243 142.495);
    --lgpd-warning: oklch(0.8851 0.1574 83.872);
    --lgpd-violation: oklch(0.7368 0.2578 25.3313);

    /* Chart Colors Dark Mode */
    --chart-1: oklch(0.6801 0.1583 276.9349);
    --chart-2: oklch(0.5854 0.2041 277.1173);
    --chart-3: oklch(0.5106 0.2301 276.9656);
    --chart-4: oklch(0.4568 0.2146 277.0229);
    --chart-5: oklch(0.3984 0.1773 277.3662);
    --chart-6: oklch(0.7792 0.2243 142.495);

    /* Sidebar Dark Mode */
    --sidebar-background: oklch(0.2392 0.0388 252.5089);
    --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
    --sidebar-primary: oklch(0.7466 0.0242 84.5921);
    --sidebar-primary-foreground: oklch(0.372 0.0497 245.2138);
    --sidebar-accent: oklch(0.3896 0.0074 59.4734);
    --sidebar-accent-foreground: oklch(0.8569 0.0111 95.1836);
    --sidebar-border: oklch(0.6776 0.0653 81.7406);
    --sidebar-ring: oklch(0.6776 0.0653 81.7406);

    /* Shadows Dark Mode */
    --shadow-color: oklch(0 0 0);
    --shadow-2xs: 2px 2px 10px 4px oklch(0 0 0 / 0.09);
    --shadow-xs: 2px 2px 10px 4px oklch(0 0 0 / 0.09);
    --shadow-sm: 2px 2px 10px 4px oklch(0 0 0 / 0.18), 2px 1px 2px 3px oklch(0 0 0 / 0.18);
    --shadow: 2px 2px 10px 4px oklch(0 0 0 / 0.18), 2px 1px 2px 3px oklch(0 0 0 / 0.18);
    --shadow-md: 2px 2px 10px 4px oklch(0 0 0 / 0.18), 2px 2px 4px 3px oklch(0 0 0 / 0.18);
    --shadow-lg: 2px 2px 10px 4px oklch(0 0 0 / 0.18), 2px 4px 6px 3px oklch(0 0 0 / 0.18);
    --shadow-xl: 2px 2px 10px 4px oklch(0 0 0 / 0.18), 2px 8px 10px 3px oklch(0 0 0 / 0.18);
    --shadow-2xl: 2px 2px 10px 4px oklch(0 0 0 / 0.45);
  }

  /* High Contrast Emergency Mode - Critical Healthcare Scenarios */
  .high-contrast,
  .emergency-mode {
    --background: oklch(1 0 0);
    --foreground: oklch(0 0 0);
    --primary: oklch(0.4 0.3 277);
    --primary-foreground: oklch(1 0 0);
    --border: oklch(0 0 0);
    --input: oklch(0.95 0 0);
    --ring: oklch(0.3 0.4 277);
    --destructive: oklch(0.5 0.3 25);
    --destructive-foreground: oklch(1 0 0);
    --success: oklch(0.4 0.3 142);
    --success-foreground: oklch(1 0 0);
    --warning: oklch(0.6 0.2 83);
    --warning-foreground: oklch(0 0 0);
  }

  .high-contrast.dark,
  .emergency-mode.dark {
    --background: oklch(0 0 0);
    --foreground: oklch(1 0 0);
    --primary: oklch(0.8 0.2 277);
    --primary-foreground: oklch(0 0 0);
    --border: oklch(1 0 0);
    --input: oklch(0.1 0 0);
    --ring: oklch(0.9 0.3 277);
  }

  /* Reduced Motion Preferences */
  @media (prefers-reduced-motion: reduce) {
    :root {
      --motion-scale: 0;
      --animation-duration-fast: 1ms;
      --animation-duration-normal: 1ms;
      --animation-duration-slow: 1ms;
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    line-height: 1.6; /* Enhanced for Portuguese medical content */
  }

  /* Typography System - Medical Terminology Optimized */
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif text-foreground;
    font-feature-settings: "kern" 1, "liga" 1;
    line-height: 1.3; /* Tighter for headings */
  }

  h1 {
    @apply text-3xl font-bold leading-tight tracking-tight;
    font-size: clamp(1.875rem, 4vw, 3rem); /* Responsive sizing */
  }

  h2 {
    @apply text-2xl font-semibold leading-tight tracking-tight;
    font-size: clamp(1.5rem, 3.5vw, 2.25rem);
  }

  h3 {
    @apply text-xl font-semibold leading-snug;
    font-size: clamp(1.25rem, 3vw, 1.875rem);
  }

  h4 {
    @apply text-lg font-medium leading-snug;
    font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  }

  h5 {
    @apply text-base font-medium leading-normal;
    font-size: clamp(1rem, 2vw, 1.25rem);
  }

  h6 {
    @apply text-sm font-medium leading-normal;
    font-size: clamp(0.875rem, 1.5vw, 1.125rem);
  }

  p {
    @apply font-sans text-foreground leading-relaxed;
    hyphens: auto; /* Better text flow for Portuguese */
    word-break: break-word;
  }

  /* Code Elements */
  code, pre, kbd, samp {
    @apply font-mono;
  }

  code:not(pre code) {
    @apply bg-muted text-muted-foreground px-1.5 py-0.5 rounded-sm text-sm;
  }

  /* Enhanced Focus Management - WCAG 2.1 AA+ */
  :focus {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
  }

  :focus:not(:focus-visible) {
    outline: none;
  }

  :focus-visible {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
    box-shadow: 0 0 0 var(--focus-ring-width) var(--focus-ring-color);
  }

  /* Interactive Elements - Healthcare Touch Targets */
  button, 
  [role="button"], 
  input[type="button"], 
  input[type="submit"], 
  input[type="reset"] {
    min-height: 44px; /* WCAG 2.1 AA minimum touch target */
    min-width: 44px;
  }

  /* Form Elements Enhancement */
  input, textarea, select {
    min-height: 44px;
  }

  /* Links Enhancement */
  a {
    @apply underline-offset-4 decoration-2;
    text-underline-offset: 0.25em;
  }

  a:hover {
    text-decoration: underline;
  }

  /* High Contrast Mode Links */
  .high-contrast a,
  .emergency-mode a {
    text-decoration: underline;
    text-decoration-thickness: 2px;
  }

  /* Emergency Mode Enhancements */
  .emergency-mode {
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  .emergency-mode button,
  .emergency-mode [role="button"] {
    min-height: 56px; /* Larger touch targets for emergency */
    font-weight: 700;
    border: 3px solid currentColor;
  }

  /* Print Styles */
  @media print {
    :root {
      --shadow-2xs: none;
      --shadow-xs: none;
      --shadow-sm: none;
      --shadow: none;
      --shadow-md: none;
      --shadow-lg: none;
      --shadow-xl: none;
      --shadow-2xl: none;
    }

    * {
      box-shadow: none !important;
      text-shadow: none !important;
    }

    body {
      background: white !important;
      color: black !important;
    }
  }
}