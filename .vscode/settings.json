{
  "npm.packageManager": "pnpm",
  "editor.defaultFormatter": "dprint.dprint",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.dprint": "explicit",
    "source.organizeImports.dprint": "explicit",
    "source.fixAll.oxlint": "explicit"
  },
  // Dprint para o dia a dia
  "[javascript]": { "editor.defaultFormatter": "dprint.dprint" },
  "[typescript]": { "editor.defaultFormatter": "dprint.dprint" },
  "[javascriptreact]": { "editor.defaultFormatter": "dprint.dprint" },
  "[typescriptreact]": { "editor.defaultFormatter": "dprint.dprint" },
  "[json]": { "editor.defaultFormatter": "dprint.dprint" },
  "[markdown]": { "editor.defaultFormatter": "dprint.dprint" },
  "[yaml]": { "editor.defaultFormatter": "esbenp.prettier-vscode" },
  "[css]": { "editor.defaultFormatter": "dprint.dprint" },
  "[scss]": { "editor.defaultFormatter": "dprint.dprint" },
  "[html]": { "editor.defaultFormatter": "dprint.dprint" },
  "[jsonc]": { "editor.defaultFormatter": "esbenp.prettier-vscode" },

  // Prettier só onde faz sentido
  "[mdx]": { "editor.defaultFormatter": "esbenp.prettier-vscode" },
  "[prisma]": { "editor.defaultFormatter": "esbenp.prettier-vscode" },
  "[graphql]": { "editor.defaultFormatter": "esbenp.prettier-vscode" },
  "[astro]": { "editor.defaultFormatter": "esbenp.prettier-vscode" },

  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.next": true,
    "**/coverage": true,
    "**/playwright-report": true,
    "**/test-results": true,
    "**/.turbo": true,
    "**/logs": true,
    "**/temp-*": true,
    "**/tmp.*": true,
    "**/archon/original_archon": true,
    "**/serena": true,
    "**/infrastructure": true,
    "**/docs": true,
    "**/turbo": true,
    "**/tools": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.next": true,
    "**/.turbo": true,
    "**/coverage": true,
    "**/playwright-report": true,
    "**/test-results": true,
    "**/logs": true,
    "**/temp-*": true,
    "**/tmp.*": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/.git/**": true,
    "**/.turbo/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/.next/**": true,
    "**/coverage/**": true,
    "**/logs/**": true,
    "**/temp-*/**": true,
    "**/tmp.*": true
  },
  "typescript.suggest.autoImports": false,
  "typescript.updateImportsOnFileMove.enabled": "prompt"
}
