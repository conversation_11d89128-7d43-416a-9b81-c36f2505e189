{"version": "2.0.0", "tasks": [{"label": "✨ Format Code", "type": "shell", "command": "pnpm", "args": ["format"], "group": "build", "detail": "Format all code using dprint"}, {"label": "🔍 Lint Code", "type": "shell", "command": "pnpm", "args": ["lint"], "group": "build", "detail": "Run oxlint to check for linting issues"}, {"label": "🔧 Fix <PERSON>", "type": "shell", "command": "pnpm", "args": ["oxlint:fix"], "group": "build", "detail": "Automatically fix linting issues with oxlint"}, {"label": "✅ Full Code Check", "type": "shell", "command": "pnpm", "args": ["ci:check"], "group": {"kind": "build", "isDefault": true}, "detail": "Run complete code validation (format check + lint + type check)"}, {"label": "🧪 Run Tests", "type": "shell", "command": "pnpm", "args": ["test:unit"], "group": {"kind": "test", "isDefault": true}, "detail": "Run all tests using Vitest with verbose output"}, {"label": "🧪 Watch Tests", "type": "shell", "command": "pnpm", "args": ["test:watch"], "group": "test", "detail": "Watch and run tests using Vitest", "isBackground": true}, {"label": "🎭 E2E Tests (Debug)", "type": "shell", "command": "npx", "args": ["playwright", "test", "--debug"], "group": "test", "detail": "Run E2E tests in debug mode"}, {"label": "🔧 Build Project", "type": "shell", "command": "pnpm", "args": ["build"], "group": "build", "detail": "Build the entire project"}, {"label": "🚀 Type Check", "type": "shell", "command": "pnpm", "args": ["type-check"], "group": "build", "detail": "Run TypeScript type checking"}, {"label": "🔧 PNPM Install", "type": "shell", "command": "pnpm", "args": ["install"], "group": "build", "detail": "Install dependencies via PNPM"}]}