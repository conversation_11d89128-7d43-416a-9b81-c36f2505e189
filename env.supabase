# Example Supabase Configuration for NeonPro Healthcare Platform
# Copy this file to your local environment file (e.g., `.env`) and fill in the real values.

# Supabase Project URL
SUPABASE_URL=your-supabase-url-here

# Supabase Anon Key (Public)
SUPABASE_ANON_KEY=your-supabase-anon-key-here

# Supabase Service Role Key (Server-side operations)
# This key has administrative privileges - keep it secure
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# Database Configuration
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Next.js Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url-here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# Healthcare Compliance Settings
HEALTHCARE_COMPLIANCE_MODE=true
PHI_PROTECTION_LEVEL=maximum
AUDIT_TRAIL_ENABLED=true
LGPD_COMPLIANCE=true